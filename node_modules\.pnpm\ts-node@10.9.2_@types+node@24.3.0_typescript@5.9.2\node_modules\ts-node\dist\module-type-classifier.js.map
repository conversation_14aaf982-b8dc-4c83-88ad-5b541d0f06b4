{"version": 3, "file": "module-type-classifier.js", "sourceRoot": "", "sources": ["../src/module-type-classifier.ts"], "names": [], "mappings": ";;;AACA,iDAAoD;AACpD,iCAAwD;AAwBxD;;;;;GAKG;AACH,SAAgB,0BAA0B,CACxC,OAAoC;IAEpC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;IAClD,MAAM,QAAQ,GACZ,SAAS,KAAK,SAAS;QACrB,CAAC,CAAC,IAAA,uBAAgB,EAAC,SAAS,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;QAChD,CAAC,CAAC,SAAS,CAAC;IAEhB,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,CAAC,CAAC,GAAG,CACzD,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE;QACnB,MAAM,OAAO,GAAG,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;QAC3C,OAAO,EAAE,OAAO,EAAE,YAAY,CAAC,QAAS,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC;IAC7D,CAAC,CACF,CAAC;IAEF,MAAM,eAAe,GACnB;QACE,OAAO,EAAE;YACP,UAAU,EAAE,MAAM;SACnB;QACD,GAAG,EAAE;YACH,UAAU,EAAE,KAAK;SAClB;QACD,GAAG,EAAE;YACH,UAAU,EAAE,KAAK;SAClB;KACF,CAAC;IACJ,MAAM,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC;IAErC,kCAAkC;IAClC,SAAS,uBAAuB,CAAC,IAAY;QAC3C,MAAM,OAAO,GAAG,aAAa,CAAC,gBAAgB,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACxE,IAAI,OAAO;YAAE,OAAO,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,cAAc,GAAG,IAAA,mBAAY,EAAC,uBAAuB,CAAC,CAAC;IAE7D,SAAS,kBAAkB,CAAC,IAAY;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO;QACL,mCAAmC,EAAE,gBAAgB,CAAC,MAAM;YAC1D,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,kBAAkB;KACvB,CAAC;AACJ,CAAC;AAhDD,gEAgDC;AAED,SAAS,YAAY,CAAC,QAAgB,EAAE,aAAqB;IAC3D,MAAM,OAAO,GAAG,IAAA,iCAAkB,EAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IAC5D,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAC9D,CAAC;AAED,SAAS,aAAa,CACpB,OAAY,EACZ,UAA4B,EAC5B,SAAiB;IAEjB,KAAK,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QAC5C,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC1B,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC,CAAC;QAEnC,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,IAAI,CAAC,SAAS,CAAC,EAAE;YAC5B,OAAO,MAAM,CAAC;SACf;KACF;AACH,CAAC", "sourcesContent": ["import type { ModuleTypeOverride, ModuleTypes } from '.';\nimport { getPatternFromSpec } from './ts-internals';\nimport { cachedLookup, normalizeSlashes } from './util';\n\n// Logic to support our `moduleTypes` option, which allows overriding node's default ESM / CJS\n// classification of `.js` files based on package.json `type` field.\n\n/**\n * Seperate internal type because `auto` is clearer than `package`, but changing\n * the public API is a breaking change.\n * @internal\n */\nexport type InternalModuleTypeOverride = 'cjs' | 'esm' | 'auto';\n/** @internal */\nexport interface ModuleTypeClassification {\n  moduleType: InternalModuleTypeOverride;\n}\n/** @internal */\nexport interface ModuleTypeClassifierOptions {\n  basePath?: string;\n  patterns?: ModuleTypes;\n}\n/** @internal */\nexport type ModuleTypeClassifier = ReturnType<\n  typeof createModuleTypeClassifier\n>;\n/**\n * @internal\n * May receive non-normalized options -- basePath and patterns -- and will normalize them\n * internally.\n * However, calls to `classifyModule` must pass pre-normalized paths!\n */\nexport function createModuleTypeClassifier(\n  options: ModuleTypeClassifierOptions\n) {\n  const { patterns, basePath: _basePath } = options;\n  const basePath =\n    _basePath !== undefined\n      ? normalizeSlashes(_basePath).replace(/\\/$/, '')\n      : undefined;\n\n  const patternTypePairs = Object.entries(patterns ?? []).map(\n    ([_pattern, type]) => {\n      const pattern = normalizeSlashes(_pattern);\n      return { pattern: parsePattern(basePath!, pattern), type };\n    }\n  );\n\n  const classifications: Record<ModuleTypeOverride, ModuleTypeClassification> =\n    {\n      package: {\n        moduleType: 'auto',\n      },\n      cjs: {\n        moduleType: 'cjs',\n      },\n      esm: {\n        moduleType: 'esm',\n      },\n    };\n  const auto = classifications.package;\n\n  // Passed path must be normalized!\n  function classifyModuleNonCached(path: string): ModuleTypeClassification {\n    const matched = matchPatterns(patternTypePairs, (_) => _.pattern, path);\n    if (matched) return classifications[matched.type];\n    return auto;\n  }\n\n  const classifyModule = cachedLookup(classifyModuleNonCached);\n\n  function classifyModuleAuto(path: String) {\n    return auto;\n  }\n\n  return {\n    classifyModuleByModuleTypeOverrides: patternTypePairs.length\n      ? classifyModule\n      : classifyModuleAuto,\n  };\n}\n\nfunction parsePattern(basePath: string, patternString: string): RegExp {\n  const pattern = getPatternFromSpec(patternString, basePath);\n  return pattern !== undefined ? new RegExp(pattern) : /(?:)/;\n}\n\nfunction matchPatterns<T>(\n  objects: T[],\n  getPattern: (t: T) => RegExp,\n  candidate: string\n): T | undefined {\n  for (let i = objects.length - 1; i >= 0; i--) {\n    const object = objects[i];\n    const pattern = getPattern(object);\n\n    if (pattern?.test(candidate)) {\n      return object;\n    }\n  }\n}\n"]}