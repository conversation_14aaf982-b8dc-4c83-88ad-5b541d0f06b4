# 🚀 插件化聊天系统

一个基于 WebSocket 的插件化聊天系统，支持群聊和私聊功能，采用插件架构设计，可以轻松扩展到不同的用户界面环境。

## ✨ 特性

- 🔌 **插件化架构** - 统一的接口设计，支持多种 UI 实现
- 💬 **群聊支持** - 所有用户可以参与的公共聊天
- 🔒 **私聊功能** - 一对一的私密聊天
- 🎯 **用户选择** - 交互式的用户列表选择
- 🌐 **跨平台** - 支持 CLI 和 Web 环境
- 📱 **响应式** - 实时消息推送和用户状态更新

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐
│   CLI Plugin    │    │   Web Plugin    │
├─────────────────┤    ├─────────────────┤
│  • inquirer     │    │  • DOM API      │
│  • chalk        │    │  • HTML/CSS     │
│  • 终端交互     │    │  • 浏览器界面   │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────┬───────────────┘
                 │
    ┌─────────────────────────────┐
    │      UIPlugin Interface     │
    ├─────────────────────────────┤
    │  • initialize()             │
    │  • showChatSelection()      │
    │  • showUserSelection()      │
    │  • promptMessage()          │
    │  • displayMessage()         │
    │  • displayError()           │
    │  • cleanup()                │
    └─────────────────────────────┘
                 │
    ┌─────────────────────────────┐
    │        Ws Class             │
    ├─────────────────────────────┤
    │  • WebSocket 连接管理       │
    │  • 消息路由和处理           │
    │  • 用户状态管理             │
    │  • 聊天逻辑控制             │
    └─────────────────────────────┘
                 │
    ┌─────────────────────────────┐
    │      WebSocket Server       │
    ├─────────────────────────────┤
    │  • 用户连接管理             │
    │  • 消息广播和转发           │
    │  • 群聊和私聊路由           │
    │  • 用户列表维护             │
    └─────────────────────────────┘
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pnpm install
```

### 2. 启动服务器

```bash
node server.ts
```

### 3. 启动客户端

#### CLI 模式
```bash
node index.ts
```

#### Web 模式
打开 `examples/web-example.html` 文件在浏览器中查看。

## 📖 使用指南

### CLI 界面操作

1. **选择聊天类型**
   - 👥 群聊 - 所有用户都能看到的公共聊天
   - 💬 私聊 - 选择特定用户进行一对一聊天

2. **私聊用户选择**
   - 使用上下箭头键导航用户列表
   - 按 Enter 键选择用户

3. **发送消息**
   - 输入消息内容
   - 按 Enter 键发送

4. **退出聊天**
   - 输入 `/quit` 或 `/exit`
   - 或使用 Ctrl+C

### 消息格式

系统支持以下消息类型：

```typescript
interface ChatMessage {
  from: string;        // 发送者ID或昵称
  to?: string;         // 私聊目标用户ID（群聊时为空）
  message: string;     // 消息内容
  timestamp: number;   // 时间戳
  type: 'group' | 'private';  // 消息类型
}
```

## 🔌 插件开发

### 创建新插件

实现 `UIPlugin` 接口来创建新的用户界面插件：

```typescript
import { UIPlugin, User, ChatMessage, ChatRoom } from './scoket.js';

export class MyCustomPlugin implements UIPlugin {
  name = 'My Custom Plugin';

  async initialize(): Promise<void> {
    // 初始化插件
  }

  async showChatSelection(rooms: ChatRoom[]): Promise<ChatRoom> {
    // 显示聊天类型选择界面
  }

  async showUserSelection(users: User[]): Promise<User> {
    // 显示用户选择界面
  }

  async promptMessage(context: string): Promise<string> {
    // 提示用户输入消息
  }

  displayMessage(message: ChatMessage): void {
    // 显示接收到的消息
  }

  displayError(error: string): void {
    // 显示错误信息
  }

  async cleanup(): Promise<void> {
    // 清理资源
  }
}
```

### 使用自定义插件

```typescript
import { Ws } from './scoket.js';
import { MyCustomPlugin } from './plugins/my-custom-plugin.js';

const ws = new Ws('ws://localhost:8080');
const plugin = new MyCustomPlugin();
ws.setUIPlugin(plugin);

await ws.startChat();
```

## 📁 项目结构

```
cli/
├── scoket.ts              # 核心 WebSocket 客户端类
├── server.ts              # WebSocket 服务器
├── index.ts               # CLI 应用入口
├── plugins/               # 插件目录
│   ├── cli-plugin.ts      # CLI 界面插件
│   └── web-plugin.ts      # Web 界面插件
├── examples/              # 示例文件
│   ├── cli-example.ts     # CLI 使用示例
│   └── web-example.html   # Web 使用示例
├── package.json           # 项目配置
└── README.md             # 项目文档
```

## 🛠️ 技术栈

- **后端**: Node.js + WebSocket (ws)
- **前端**: TypeScript + 插件架构
- **CLI**: inquirer + chalk
- **Web**: 原生 DOM API
- **包管理**: pnpm

## 🔮 未来扩展

插件化架构支持轻松添加新功能：

- 📱 **移动端插件** - React Native 或其他移动框架
- 🖥️ **桌面端插件** - Electron 或 Tauri
- 🎮 **游戏引擎插件** - Unity 或 Unreal Engine
- 🤖 **机器人插件** - 自动化聊天机器人
- 📊 **数据分析插件** - 聊天数据统计和分析

## 📝 许可证

ISC License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！
