{"version": 3, "file": "file-extensions.js", "sourceRoot": "", "sources": ["../src/file-extensions.ts"], "names": [], "mappings": ";;;AAEA,iCAAsC;AAWtC,MAAM,eAAe,GAAG,IAAI,GAAG,CAAiB;IAC9C,CAAC,KAAK,EAAE,KAAK,CAAC;IACd,CAAC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC,MAAM,EAAE,KAAK,CAAC;IACf,CAAC,MAAM,EAAE,MAAM,CAAC;IAChB,CAAC,MAAM,EAAE,MAAM,CAAC;CACjB,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,IAAI,GAAG,CAA4B;IAC/D,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;IAChB,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACzB,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;IAClB,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAC;CACnB,CAAC,CAAC;AAEH,4CAA4C;AAC5C,MAAM,qBAAqB,GAAsB;IAC/C,KAAK;IACL,OAAO;IACP,OAAO;IACP,MAAM;IACN,MAAM;CACP,CAAC;AAEF,iEAAiE;AACjE,iBAAiB;AACjB,kGAAkG;AAClG,sBAAsB;AACtB,yEAAyE;AACzE,4BAA4B;AAE5B,MAAM,qBAAqB,GAAsB;IAC/C,KAAK;IACL,MAAM;IACN,MAAM;IACN,MAAM;IACN,MAAM;CACP,CAAC;AAEF;;;GAGG;AACH,SAAgB,aAAa,CAC3B,MAA6B,EAC7B,OAAwB,EACxB,SAAiB;IAEjB,8EAA8E;IAC9E,MAAM,oBAAoB,GAAG,IAAA,mBAAY,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAE9D,MAAM,+BAA+B,GAAa,EAAE,CAAC;IACrD,IAAI,CAAC,oBAAoB;QACvB,+BAA+B,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAEvE,MAAM,uCAAuC,GAAG,KAAK,CAAC,IAAI,CACxD,IAAI,GAAG,CAAC;QACN,GAAG,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,GAAG,qBAAqB;QACxB,GAAG,qBAAqB;KACzB,CAAC,CACH,CAAC;IAEF,MAAM,kBAAkB,GAAa,CAAC,KAAK,CAAC,CAAC;IAC7C,MAAM,mBAAmB,GAAa,EAAE,CAAC;IAEzC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG;QAAE,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzD,IAAI,oBAAoB;QAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClE,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE;QAC1B,kBAAkB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG;YAAE,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzD,IAAI,oBAAoB;YAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACnE;IAED,MAAM,gBAAgB,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,mBAAmB,CAAC,CAAC;IACzE,MAAM,QAAQ,GAAG,uCAAuC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CACtE,gBAAgB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC/B,CAAC;IAEF,MAAM,6BAA6B,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CACzE,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CACvB,CAAC;IAEF;;;;;;OAMG;IACH,MAAM,CAAC,GAAG,uCAAuC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAC/D,CAAC,GAAG,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC3E,CAAC;IACF,MAAM,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CACzC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC7C,CAAC;IACF,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,MAAM,kBAAkB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC7E,MAAM,sBAAsB,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,EAAE,CAC9C,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC7D,CAAC;IAEF,kGAAkG;IAClG,gEAAgE;IAChE,MAAM,4CAA4C,GAAG,KAAK,CAAC,IAAI,CAC7D,IAAI,GAAG,CAAC,CAAC,GAAG,sBAAsB,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CACvD,CAAC;IACF,4DAA4D;IAC5D,MAAM,8BAA8B,GAAG,KAAK,CAAC,IAAI,CAC/C,IAAI,GAAG,CAAC,CAAC,GAAG,iBAAiB,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAClD,CAAC;IAEF,OAAO;QACL,mGAAmG;QACnG,QAAQ;QACR,uFAAuF;QACvF,qBAAqB;QACrB,wDAAwD;QACxD,6BAA6B;QAC7B;;;WAGG;QACH,eAAe;QACf;;;;;;;;;;;WAWG;QACH,qBAAqB;QACrB;;;WAGG;QACH,+BAA+B;QAC/B;;WAEG;QACH,4CAA4C;QAC5C;;WAEG;QACH,8BAA8B;QAC9B,kBAAkB;QAClB,kBAAkB;QAClB,kBAAkB;QAClB,iBAAiB;KAClB,CAAC;AACJ,CAAC;AAjHD,sCAiHC", "sourcesContent": ["import type * as _ts from 'typescript';\nimport type { RegisterOptions } from '.';\nimport { versionGteLt } from './util';\n\n/**\n * Centralized specification of how we deal with file extensions based on\n * project options:\n * which ones we do/don't support, in what situations, etc.  These rules drive\n * logic elsewhere.\n * @internal\n * */\nexport type Extensions = ReturnType<typeof getExtensions>;\n\nconst nodeEquivalents = new Map<string, string>([\n  ['.ts', '.js'],\n  ['.tsx', '.js'],\n  ['.jsx', '.js'],\n  ['.mts', '.mjs'],\n  ['.cts', '.cjs'],\n]);\n\nconst tsResolverEquivalents = new Map<string, readonly string[]>([\n  ['.ts', ['.js']],\n  ['.tsx', ['.js', '.jsx']],\n  ['.mts', ['.mjs']],\n  ['.cts', ['.cjs']],\n]);\n\n// All extensions understood by vanilla node\nconst vanillaNodeExtensions: readonly string[] = [\n  '.js',\n  '.json',\n  '.node',\n  '.mjs',\n  '.cjs',\n];\n\n// Extensions added by vanilla node's require() if you omit them:\n// js, json, node\n// Extensions added by vanilla node if you omit them with --experimental-specifier-resolution=node\n// js, json, node, mjs\n// Extensions added by ESM codepath's legacy package.json \"main\" resolver\n// js, json, node (not mjs!)\n\nconst nodeDoesNotUnderstand: readonly string[] = [\n  '.ts',\n  '.tsx',\n  '.jsx',\n  '.cts',\n  '.mts',\n];\n\n/**\n * [MUST_UPDATE_FOR_NEW_FILE_EXTENSIONS]\n * @internal\n */\nexport function getExtensions(\n  config: _ts.ParsedCommandLine,\n  options: RegisterOptions,\n  tsVersion: string\n) {\n  // TS 4.5 is first version to understand .cts, .mts, .cjs, and .mjs extensions\n  const tsSupportsMtsCtsExts = versionGteLt(tsVersion, '4.5.0');\n\n  const requiresHigherTypescriptVersion: string[] = [];\n  if (!tsSupportsMtsCtsExts)\n    requiresHigherTypescriptVersion.push('.cts', '.cjs', '.mts', '.mjs');\n\n  const allPossibleExtensionsSortedByPreference = Array.from(\n    new Set([\n      ...(options.preferTsExts ? nodeDoesNotUnderstand : []),\n      ...vanillaNodeExtensions,\n      ...nodeDoesNotUnderstand,\n    ])\n  );\n\n  const compiledJsUnsorted: string[] = ['.ts'];\n  const compiledJsxUnsorted: string[] = [];\n\n  if (config.options.jsx) compiledJsxUnsorted.push('.tsx');\n  if (tsSupportsMtsCtsExts) compiledJsUnsorted.push('.mts', '.cts');\n  if (config.options.allowJs) {\n    compiledJsUnsorted.push('.js');\n    if (config.options.jsx) compiledJsxUnsorted.push('.jsx');\n    if (tsSupportsMtsCtsExts) compiledJsUnsorted.push('.mjs', '.cjs');\n  }\n\n  const compiledUnsorted = [...compiledJsUnsorted, ...compiledJsxUnsorted];\n  const compiled = allPossibleExtensionsSortedByPreference.filter((ext) =>\n    compiledUnsorted.includes(ext)\n  );\n\n  const compiledNodeDoesNotUnderstand = nodeDoesNotUnderstand.filter((ext) =>\n    compiled.includes(ext)\n  );\n\n  /**\n   * TS's resolver can resolve foo.js to foo.ts, by replacing .js extension with several source extensions.\n   * IMPORTANT: Must preserve ordering according to preferTsExts!\n   *            Must include the .js/.mjs/.cjs extension in the array!\n   *            This affects resolution behavior!\n   * [MUST_UPDATE_FOR_NEW_FILE_EXTENSIONS]\n   */\n  const r = allPossibleExtensionsSortedByPreference.filter((ext) =>\n    [...compiledUnsorted, '.js', '.mjs', '.cjs', '.mts', '.cts'].includes(ext)\n  );\n  const replacementsForJs = r.filter((ext) =>\n    ['.js', '.jsx', '.ts', '.tsx'].includes(ext)\n  );\n  const replacementsForJsx = r.filter((ext) => ['.jsx', '.tsx'].includes(ext));\n  const replacementsForMjs = r.filter((ext) => ['.mjs', '.mts'].includes(ext));\n  const replacementsForCjs = r.filter((ext) => ['.cjs', '.cts'].includes(ext));\n  const replacementsForJsOrMjs = r.filter((ext) =>\n    ['.js', '.jsx', '.ts', '.tsx', '.mjs', '.mts'].includes(ext)\n  );\n\n  // Node allows omitting .js or .mjs extension in certain situations (CJS, ESM w/experimental flag)\n  // So anything that compiles to .js or .mjs can also be omitted.\n  const experimentalSpecifierResolutionAddsIfOmitted = Array.from(\n    new Set([...replacementsForJsOrMjs, '.json', '.node'])\n  );\n  // Same as above, except node curiuosly doesn't do .mjs here\n  const legacyMainResolveAddsIfOmitted = Array.from(\n    new Set([...replacementsForJs, '.json', '.node'])\n  );\n\n  return {\n    /** All file extensions we transform, ordered by resolution preference according to preferTsExts */\n    compiled,\n    /** Resolved extensions that vanilla node will not understand; we should handle them */\n    nodeDoesNotUnderstand,\n    /** Like the above, but only the ones we're compiling */\n    compiledNodeDoesNotUnderstand,\n    /**\n     * Mapping from extensions understood by tsc to the equivalent for node,\n     * as far as getFormat is concerned.\n     */\n    nodeEquivalents,\n    /**\n     * Mapping from extensions rejected by TSC in import specifiers, to the\n     * possible alternatives that TS's resolver will accept.\n     *\n     * When we allow users to opt-in to .ts extensions in import specifiers, TS's\n     * resolver requires us to replace the .ts extensions with .js alternatives.\n     * Otherwise, resolution fails.\n     *\n     * Note TS's resolver is only used by, and only required for, typechecking.\n     * This is separate from node's resolver, which we hook separately and which\n     * does not require this mapping.\n     */\n    tsResolverEquivalents,\n    /**\n     * Extensions that we can support if the user upgrades their typescript version.\n     * Used when raising hints.\n     */\n    requiresHigherTypescriptVersion,\n    /**\n     * --experimental-specifier-resolution=node will add these extensions.\n     */\n    experimentalSpecifierResolutionAddsIfOmitted,\n    /**\n     * ESM loader will add these extensions to package.json \"main\" field\n     */\n    legacyMainResolveAddsIfOmitted,\n    replacementsForMjs,\n    replacementsForCjs,\n    replacementsForJsx,\n    replacementsForJs,\n  };\n}\n"]}