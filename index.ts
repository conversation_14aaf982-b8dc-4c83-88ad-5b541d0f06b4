import { Ws } from "./scoket.ts";
import { CLIPlugin } from "./plugins/cli-plugin.ts";

async function main() {
  try {
    // 创建 WebSocket 客户端
    const ws = new Ws('ws://localhost:8081');

    // 创建并设置 CLI 插件
    const cliPlugin = new CLIPlugin();
    ws.setUIPlugin(cliPlugin);

    // 开始聊天
    await ws.startChat();

  } catch (error) {
    console.error('应用启动失败:', error);
    process.exit(1);
  }
}

// 处理程序退出
process.on('SIGINT', () => {
  console.log('\n👋 程序已退出');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 程序已退出');
  process.exit(0);
});

// 启动应用
main().catch(error => {
  console.error('未处理的错误:', error);
  process.exit(1);
});
