import { WebSocketServer, WebSocket } from 'ws';

interface UserInfo {
  id: string;       // 唯一 ID
  ip: string;       // IP
  nickname?: string; // 可以在登录/注册时指定
}

interface ChatMessage {
  from: string;
  to?: string;
  message: string;
  timestamp: number;
  type: 'group' | 'private';
}

const users = new Map<WebSocket, UserInfo>();

const PORT = process.env.PORT ? parseInt(process.env.PORT) : 8081;
const wss = new WebSocketServer({ port: PORT });

console.log(`🚀 WebSocket 服务器启动在端口 ${PORT}`);

wss.on('connection', (ws, req) => {
  const ip = req.socket.remoteAddress ?? 'unknown';
  // 简单生成一个唯一 ID（可以用 uuid）
  const id = Date.now().toString();
  const nickname = `用户${id.slice(-4)}`;

  users.set(ws, { id, ip, nickname });

  console.log(`✅ 新用户连接: ${nickname} (${id}) - ${ip}`);

  // 发送欢迎消息和用户列表
  ws.send(JSON.stringify({
    type: 'welcome',
    userId: id,
    nickname: nickname
  }));

  // 发送当前用户列表
  sendUserList(ws);

  ws.on('message', raw => {
    try {
      const data = JSON.parse(raw.toString());
      console.log(`收到来自 ${nickname} 的消息:`, data);

      switch (data.type) {
        case 'getUserList':
          sendUserList(ws);
          break;

        case 'getRoomList':
          sendRoomList(ws);
          break;

        case 'chatMessage':
          handleChatMessage(ws, data);
          break;

        default:
          // 兼容旧的简单消息格式
          handleLegacyMessage(ws, raw.toString());
      }
    } catch (error) {
      console.error('解析消息失败:', error);
      // 尝试作为简单文本消息处理
      handleLegacyMessage(ws, raw.toString());
    }
  });

  ws.on('close', () => {
    const user = users.get(ws);
    console.log(`❌ 用户断开连接: ${user?.nickname || 'Unknown'}`);
    users.delete(ws);

    // 通知其他用户有人离开
    broadcastUserList();
  });

  ws.on('error', (error) => {
    console.error('WebSocket 错误:', error);
  });

  // 通知其他用户有新用户加入
  broadcastUserList();
});

function sendUserList(ws: WebSocket): void {
  const userList = Array.from(users.values()).map(user => ({
    id: user.id,
    nickname: user.nickname,
    ip: user.ip
  }));

  ws.send(JSON.stringify({
    type: 'userList',
    users: userList
  }));
}

function sendRoomList(ws: WebSocket): void {
  const rooms = [
    { id: 'group', name: '群聊', type: 'group' },
    { id: 'private', name: '私聊', type: 'private' }
  ];

  ws.send(JSON.stringify({
    type: 'roomList',
    rooms: rooms
  }));
}

function broadcastUserList(): void {
  const userList = Array.from(users.values()).map(user => ({
    id: user.id,
    nickname: user.nickname,
    ip: user.ip
  }));

  const message = JSON.stringify({
    type: 'userList',
    users: userList
  });

  for (const [client] of users) {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  }
}

function handleChatMessage(senderWs: WebSocket, data: ChatMessage): void {
  const sender = users.get(senderWs);
  if (!sender) return;

  const message: ChatMessage = {
    from: sender.nickname || sender.id,
    to: data.to,
    message: data.message,
    timestamp: Date.now(),
    type: data.type
  };

  console.log(`📨 ${message.type === 'private' ? '私聊' : '群聊'} 消息: ${message.from} -> ${message.to || '所有人'}: ${message.message}`);

  if (message.type === 'private' && message.to) {
    // 私聊消息：只发送给目标用户和发送者
    const targetUser = Array.from(users.entries()).find(([_, user]) => user.id === message.to);

    if (targetUser) {
      const [targetWs] = targetUser;

      // 发送给目标用户
      if (targetWs.readyState === WebSocket.OPEN) {
        targetWs.send(JSON.stringify({
          ...message,
          type: 'message'
        }));
      }

      // 发送给发送者（确认消息）
      if (senderWs.readyState === WebSocket.OPEN) {
        senderWs.send(JSON.stringify({
          ...message,
          from: 'self', // 标记为自己发送的消息
          type: 'message'
        }));
      }
    } else {
      // 目标用户不存在
      senderWs.send(JSON.stringify({
        type: 'error',
        message: '目标用户不存在或已离线'
      }));
    }
  } else {
    // 群聊消息：广播给所有用户
    const broadcastMessage = JSON.stringify({
      ...message,
      type: 'message'
    });

    for (const [client] of users) {
      if (client.readyState === WebSocket.OPEN) {
        // 发送者看到的是自己的消息
        if (client === senderWs) {
          client.send(JSON.stringify({
            ...message,
            from: 'self',
            type: 'message'
          }));
        } else {
          client.send(broadcastMessage);
        }
      }
    }
  }
}

function handleLegacyMessage(senderWs: WebSocket, message: string): void {
  const sender = users.get(senderWs);
  if (!sender) return;

  console.log(`📨 传统消息来自 ${sender.nickname}: ${message}`);

  // 作为群聊消息处理
  const chatMessage: ChatMessage = {
    from: sender.nickname || sender.id,
    message: message,
    timestamp: Date.now(),
    type: 'group'
  };

  const broadcastMessage = JSON.stringify({
    ...chatMessage,
    type: 'message'
  });

  for (const [client] of users) {
    if (client.readyState === WebSocket.OPEN) {
      if (client === senderWs) {
        client.send(JSON.stringify({
          ...chatMessage,
          from: 'self',
          type: 'message'
        }));
      } else {
        client.send(broadcastMessage);
      }
    }
  }
}
