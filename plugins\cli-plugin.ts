import inquirer from 'inquirer';
import chalk from 'chalk';
import { UIPlugin, User, ChatMessage, ChatRoom } from '../scoket.ts';

export class CLIPlugin implements UIPlugin {
  name = 'CLI Plugin';
  private isInitialized = false;

  async initialize(): Promise<void> {
    console.clear();
    console.log(chalk.blue.bold('🚀 欢迎使用聊天客户端'));
    console.log(chalk.gray('输入 /quit 或 /exit 退出聊天\n'));
    this.isInitialized = true;
  }

  async showChatSelection(rooms: ChatRoom[]): Promise<ChatRoom> {
    const choices = rooms.map(room => ({
      name: `${room.type === 'group' ? '👥' : '💬'} ${room.name}`,
      value: room
    }));

    const { selectedRoom } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedRoom',
        message: chalk.cyan('请选择聊天类型:'),
        choices,
        pageSize: 10
      }
    ]);

    return selectedRoom;
  }

  async showUserSelection(users: User[]): Promise<User> {
    if (users.length === 0) {
      throw new Error('没有可用的用户');
    }

    const choices = users.map(user => ({
      name: `👤 ${user.nickname || user.id} ${user.ip ? `(${user.ip})` : ''}`,
      value: user
    }));

    const { selectedUser } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedUser',
        message: chalk.cyan('请选择要私聊的用户:'),
        choices,
        pageSize: 15
      }
    ]);

    return selectedUser;
  }

  async promptMessage(context: string): Promise<string> {
    console.log(chalk.green(`\n📍 ${context}`));
    
    const { message } = await inquirer.prompt([
      {
        type: 'input',
        name: 'message',
        message: chalk.yellow('输入消息:'),
        validate: (input: string) => {
          if (input.trim().length === 0) {
            return '消息不能为空';
          }
          return true;
        }
      }
    ]);

    return message;
  }

  displayMessage(message: ChatMessage): void {
    const timestamp = new Date(message.timestamp).toLocaleTimeString();
    const typeIcon = message.type === 'group' ? '👥' : '💬';
    const fromDisplay = message.from === 'self' ? '我' : message.from;
    
    if (message.type === 'private') {
      console.log(
        chalk.magenta(`[${timestamp}] ${typeIcon} ${fromDisplay}: `) + 
        chalk.white(message.message)
      );
    } else {
      console.log(
        chalk.blue(`[${timestamp}] ${typeIcon} ${fromDisplay}: `) + 
        chalk.white(message.message)
      );
    }
  }

  displayError(error: string): void {
    console.log(chalk.red(`❌ 错误: ${error}`));
  }

  async cleanup(): Promise<void> {
    console.log(chalk.yellow('\n👋 聊天已结束'));
    this.isInitialized = false;
  }

  // 辅助方法：显示帮助信息
  showHelp(): void {
    console.log(chalk.cyan('\n📖 可用命令:'));
    console.log(chalk.gray('  /quit, /exit - 退出聊天'));
    console.log(chalk.gray('  /help - 显示帮助信息'));
    console.log(chalk.gray('  其他任何文本 - 发送消息\n'));
  }

  // 辅助方法：显示连接状态
  showConnectionStatus(connected: boolean): void {
    const status = connected ? 
      chalk.green('🟢 已连接') : 
      chalk.red('🔴 未连接');
    console.log(`状态: ${status}`);
  }
}
