// 基础类型定义
export interface User {
  id: string;
  nickname?: string;
  ip?: string;
}

export interface ChatMessage {
  from: string;
  to?: string; // 如果为空则是群聊
  message: string;
  timestamp: number;
  type: 'group' | 'private';
}

export interface ChatRoom {
  id: string;
  name: string;
  type: 'group' | 'private';
  participants?: User[];
}

// 插件接口定义
export interface UIPlugin {
  name: string;
  initialize(): Promise<void>;
  showChatSelection(rooms: ChatRoom[]): Promise<ChatRoom>;
  showUserSelection(users: User[]): Promise<User>;
  promptMessage(context: string): Promise<string>;
  displayMessage(message: ChatMessage): void;
  displayError(error: string): void;
  cleanup(): Promise<void>;
}

// WebSocket 客户端类
export class Ws {
  private socket: WebSocket;
  private users: Map<string, User> = new Map();
  private currentRoom: ChatRoom | null = null;
  private uiPlugin: UIPlugin | null = null;
  private url: string;

  constructor(url: string = 'ws://localhost:8081') {
    this.url = url;
    this.socket = new WebSocket(url);
    this.setupEventListeners();
  }

  // 设置 UI 插件
  setUIPlugin(plugin: UIPlugin): void {
    this.uiPlugin = plugin;
  }

  private setupEventListeners(): void {
    this.socket.addEventListener('open', this.handleOpen.bind(this));
    this.socket.addEventListener('message', this.handleMessage.bind(this));
    this.socket.addEventListener('close', this.handleClose.bind(this));
    this.socket.addEventListener('error', this.handleError.bind(this));
  }

  private handleOpen(_event: Event): void {
    console.log('WebSocket connection established!');
    // 请求用户列表和聊天室列表
    this.requestUserList();
    this.requestRoomList();
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);

      switch (data.type) {
        case 'userList':
          this.updateUserList(data.users);
          break;
        case 'roomList':
          this.updateRoomList(data.rooms);
          break;
        case 'message':
          this.displayIncomingMessage(data);
          break;
        default:
          console.log('Unknown message type:', data);
      }
    } catch (error) {
      console.error('Error parsing message:', error);
    }
  }

  private handleClose(event: CloseEvent): void {
    console.log('WebSocket connection closed:', event.code, event.reason);
    if (this.uiPlugin) {
      this.uiPlugin.displayError('Connection lost');
    }
  }

  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    if (this.uiPlugin) {
      this.uiPlugin.displayError('WebSocket connection error');
    }
  }

  private requestUserList(): void {
    this.send({ type: 'getUserList' });
  }

  private requestRoomList(): void {
    this.send({ type: 'getRoomList' });
  }

  private updateUserList(users: User[]): void {
    this.users.clear();
    users.forEach(user => this.users.set(user.id, user));
  }

  private updateRoomList(_rooms: ChatRoom[]): void {
    // 可以存储房间列表供后续使用
  }

  private displayIncomingMessage(messageData: ChatMessage): void {
    if (this.uiPlugin) {
      this.uiPlugin.displayMessage(messageData);
    } else {
      console.log(`Message from ${messageData.from}: ${messageData.message}`);
    }
  }

  // 公共方法
  async startChat(): Promise<void> {
    if (!this.uiPlugin) {
      throw new Error('UI plugin not set');
    }

    await this.uiPlugin.initialize();

    try {
      // 等待连接建立
      await this.waitForConnection();

      // 选择聊天类型和目标
      await this.selectChatTarget();

      // 开始聊天循环
      await this.chatLoop();
    } catch (error) {
      this.uiPlugin.displayError(`Error: ${error}`);
    }
  }

  private waitForConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket.readyState === WebSocket.OPEN) {
        resolve();
        return;
      }

      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout'));
      }, 5000);

      this.socket.addEventListener('open', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.socket.addEventListener('error', () => {
        clearTimeout(timeout);
        reject(new Error('Connection failed'));
      });
    });
  }

  private async selectChatTarget(): Promise<void> {
    if (!this.uiPlugin) return;

    // 创建聊天选项
    const rooms: ChatRoom[] = [
      { id: 'group', name: '群聊', type: 'group' },
      { id: 'private', name: '私聊', type: 'private' }
    ];

    const selectedRoom = await this.uiPlugin.showChatSelection(rooms);

    if (selectedRoom.type === 'private') {
      // 如果选择私聊，需要选择用户
      const userList = Array.from(this.users.values());
      if (userList.length === 0) {
        this.uiPlugin.displayError('No users available for private chat');
        return;
      }

      const selectedUser = await this.uiPlugin.showUserSelection(userList);
      this.currentRoom = {
        id: `private_${selectedUser.id}`,
        name: `私聊 - ${selectedUser.nickname || selectedUser.id}`,
        type: 'private',
        participants: [selectedUser]
      };
    } else {
      this.currentRoom = selectedRoom;
    }
  }

  private async chatLoop(): Promise<void> {
    if (!this.uiPlugin || !this.currentRoom) return;

    while (true) {
      try {
        const context = `当前聊天: ${this.currentRoom.name}`;
        const message = await this.uiPlugin.promptMessage(context);

        if (message.toLowerCase() === '/quit' || message.toLowerCase() === '/exit') {
          break;
        }

        if (message.trim()) {
          this.sendChatMessage(message);
        }
      } catch (error) {
        this.uiPlugin.displayError(`Input error: ${error}`);
        break;
      }
    }

    await this.uiPlugin.cleanup();
  }

  private sendChatMessage(message: string): void {
    if (!this.currentRoom) return;

    const chatMessage: ChatMessage = {
      from: 'self', // 这里应该是当前用户的ID
      message,
      timestamp: Date.now(),
      type: this.currentRoom.type,
      to: this.currentRoom.type === 'private' ? this.currentRoom.participants?.[0]?.id : undefined
    };

    this.send({
      ...chatMessage,
      type: 'chatMessage'
    });
  }

  private send(data: any): void {
    if (this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(data));
    }
  }

  // 获取用户列表（供插件使用）
  getUsers(): User[] {
    return Array.from(this.users.values());
  }

  // 关闭连接
  disconnect(): void {
    this.socket.close();
  }
}
