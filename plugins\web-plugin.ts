import { U<PERSON>lugin, User, ChatMessage, ChatRoom } from '../scoket.ts';

export class WebPlugin implements UIPlugin {
  name = 'Web Plugin';
  private container: HTMLElement | null = null;
  private messageContainer: HTMLElement | null = null;
  private inputElement: HTMLInputElement | null = null;

  async initialize(): Promise<void> {
    // 创建基本的 HTML 结构
    this.createUI();
    console.log('Web UI initialized');
  }

  private createUI(): void {
    // 创建主容器
    this.container = document.createElement('div');
    this.container.id = 'chat-container';
    this.container.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #f0f0f0;
      font-family: Arial, sans-serif;
      display: flex;
      flex-direction: column;
    `;

    // 创建消息显示区域
    this.messageContainer = document.createElement('div');
    this.messageContainer.id = 'messages';
    this.messageContainer.style.cssText = `
      flex: 1;
      overflow-y: auto;
      padding: 20px;
      background: white;
      margin: 10px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    `;

    // 创建输入区域
    const inputContainer = document.createElement('div');
    inputContainer.style.cssText = `
      padding: 20px;
      background: white;
      margin: 0 10px 10px 10px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      display: flex;
      gap: 10px;
    `;

    this.inputElement = document.createElement('input');
    this.inputElement.type = 'text';
    this.inputElement.placeholder = '输入消息...';
    this.inputElement.style.cssText = `
      flex: 1;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    `;

    const sendButton = document.createElement('button');
    sendButton.textContent = '发送';
    sendButton.style.cssText = `
      padding: 10px 20px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    `;

    inputContainer.appendChild(this.inputElement);
    inputContainer.appendChild(sendButton);

    this.container.appendChild(this.messageContainer);
    this.container.appendChild(inputContainer);

    document.body.appendChild(this.container);
  }

  async showChatSelection(rooms: ChatRoom[]): Promise<ChatRoom> {
    return new Promise((resolve) => {
      // 创建选择界面
      const modal = this.createModal('选择聊天类型');
      const list = document.createElement('div');
      
      rooms.forEach(room => {
        const button = document.createElement('button');
        button.textContent = `${room.type === 'group' ? '👥' : '💬'} ${room.name}`;
        button.style.cssText = `
          display: block;
          width: 100%;
          padding: 15px;
          margin: 5px 0;
          background: #f8f9fa;
          border: 1px solid #ddd;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        `;
        
        button.onclick = () => {
          document.body.removeChild(modal);
          resolve(room);
        };
        
        list.appendChild(button);
      });
      
      modal.querySelector('.modal-content')?.appendChild(list);
    });
  }

  async showUserSelection(users: User[]): Promise<User> {
    return new Promise((resolve, reject) => {
      if (users.length === 0) {
        reject(new Error('没有可用的用户'));
        return;
      }

      const modal = this.createModal('选择用户');
      const list = document.createElement('div');
      
      users.forEach(user => {
        const button = document.createElement('button');
        button.textContent = `👤 ${user.nickname || user.id}`;
        button.style.cssText = `
          display: block;
          width: 100%;
          padding: 15px;
          margin: 5px 0;
          background: #f8f9fa;
          border: 1px solid #ddd;
          border-radius: 4px;
          cursor: pointer;
          font-size: 16px;
        `;
        
        button.onclick = () => {
          document.body.removeChild(modal);
          resolve(user);
        };
        
        list.appendChild(button);
      });
      
      modal.querySelector('.modal-content')?.appendChild(list);
    });
  }

  async promptMessage(context: string): Promise<string> {
    // 更新上下文显示
    this.updateContext(context);
    
    return new Promise((resolve) => {
      if (!this.inputElement) return;
      
      const handleSubmit = () => {
        const message = this.inputElement!.value.trim();
        if (message) {
          this.inputElement!.value = '';
          resolve(message);
        }
      };

      // 监听回车键
      this.inputElement.onkeypress = (e) => {
        if (e.key === 'Enter') {
          handleSubmit();
        }
      };

      // 监听发送按钮点击
      const sendButton = this.container?.querySelector('button');
      if (sendButton) {
        sendButton.onclick = handleSubmit;
      }
    });
  }

  displayMessage(message: ChatMessage): void {
    if (!this.messageContainer) return;

    const messageDiv = document.createElement('div');
    const timestamp = new Date(message.timestamp).toLocaleTimeString();
    const typeIcon = message.type === 'group' ? '👥' : '💬';
    const fromDisplay = message.from === 'self' ? '我' : message.from;
    
    messageDiv.innerHTML = `
      <div style="margin: 10px 0; padding: 10px; background: ${message.from === 'self' ? '#e3f2fd' : '#f5f5f5'}; border-radius: 8px;">
        <div style="font-size: 12px; color: #666; margin-bottom: 5px;">
          ${typeIcon} ${fromDisplay} - ${timestamp}
        </div>
        <div style="font-size: 14px; color: #333;">
          ${message.message}
        </div>
      </div>
    `;

    this.messageContainer.appendChild(messageDiv);
    this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
  }

  displayError(error: string): void {
    if (!this.messageContainer) return;

    const errorDiv = document.createElement('div');
    errorDiv.innerHTML = `
      <div style="margin: 10px 0; padding: 10px; background: #ffebee; border-radius: 8px; color: #c62828;">
        ❌ 错误: ${error}
      </div>
    `;

    this.messageContainer.appendChild(errorDiv);
    this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
  }

  async cleanup(): Promise<void> {
    if (this.container && document.body.contains(this.container)) {
      document.body.removeChild(this.container);
    }
    console.log('Web UI cleaned up');
  }

  private createModal(title: string): HTMLElement {
    const modal = document.createElement('div');
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    `;

    const content = document.createElement('div');
    content.className = 'modal-content';
    content.style.cssText = `
      background: white;
      padding: 20px;
      border-radius: 8px;
      max-width: 400px;
      width: 90%;
      max-height: 80%;
      overflow-y: auto;
    `;

    const titleElement = document.createElement('h2');
    titleElement.textContent = title;
    titleElement.style.cssText = `
      margin: 0 0 20px 0;
      color: #333;
      text-align: center;
    `;

    content.appendChild(titleElement);
    modal.appendChild(content);
    document.body.appendChild(modal);

    return modal;
  }

  private updateContext(context: string): void {
    // 可以在界面上显示当前聊天上下文
    console.log(`Context: ${context}`);
  }
}
