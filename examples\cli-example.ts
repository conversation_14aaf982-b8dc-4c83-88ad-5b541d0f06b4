import { Ws } from "../scoket.ts";
import { CLIPlugin } from "../plugins/cli-plugin.ts";

/**
 * CLI 环境使用示例
 * 这个示例展示了如何在命令行环境中使用聊天客户端
 */
async function runCLIExample() {
  console.log('🚀 启动 CLI 聊天客户端示例...\n');

  try {
    // 创建 WebSocket 客户端
    const ws = new Ws('ws://localhost:8080');
    
    // 创建并配置 CLI 插件
    const cliPlugin = new CLIPlugin();
    ws.setUIPlugin(cliPlugin);
    
    // 显示连接状态
    cliPlugin.showConnectionStatus(false);
    
    console.log('正在连接到服务器...');
    
    // 开始聊天
    await ws.startChat();
    
    // 聊天结束后断开连接
    ws.disconnect();
    
  } catch (error) {
    console.error('❌ CLI 示例运行失败:', error);
    process.exit(1);
  }
}

// 处理程序退出信号
process.on('SIGINT', () => {
  console.log('\n\n👋 收到退出信号，正在关闭...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n\n👋 收到终止信号，正在关闭...');
  process.exit(0);
});

// 运行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runCLIExample().catch(error => {
    console.error('未处理的错误:', error);
    process.exit(1);
  });
}

export { runCLIExample };
