<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web 聊天客户端示例</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }
        
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        
        .description {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .button:hover {
            background: #5a6fd8;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
        }
        
        .status.connecting {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Web 聊天客户端示例</h1>
        <div class="description">
            这个示例展示了如何在 Web 环境中使用聊天客户端。<br>
            点击下面的按钮开始体验插件化的聊天界面。
        </div>
        
        <button class="button" onclick="startWebChat()">🚀 启动 Web 聊天</button>
        <button class="button" onclick="showPluginInfo()">📋 插件信息</button>
        
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script type="module">
        // 注意：在实际使用中，你需要将 TypeScript 编译为 JavaScript
        // 或者使用支持 TypeScript 的构建工具
        
        async function startWebChat() {
            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status connecting';
            statusDiv.textContent = '🔄 正在连接到服务器...';
            
            try {
                // 动态导入模块（需要编译后的 JS 文件）
                const { Ws } = await import('../scoket.js');
                const { WebPlugin } = await import('../plugins/web-plugin.js');
                
                // 创建 WebSocket 客户端
                const ws = new Ws('ws://localhost:8080');
                
                // 创建并设置 Web 插件
                const webPlugin = new WebPlugin();
                ws.setUIPlugin(webPlugin);
                
                statusDiv.className = 'status connected';
                statusDiv.textContent = '✅ 已连接，正在初始化界面...';
                
                // 开始聊天
                await ws.startChat();
                
            } catch (error) {
                console.error('Web 示例启动失败:', error);
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ 连接失败: ${error.message}`;
            }
        }
        
        function showPluginInfo() {
            alert(`
🔌 插件化架构说明：

✨ 当前支持的插件：
• CLI Plugin - 命令行界面插件
• Web Plugin - 网页界面插件

🚀 插件特性：
• 统一的接口设计
• 可扩展的架构
• 环境无关的核心逻辑
• 易于添加新的 UI 实现

💡 扩展方式：
实现 UIPlugin 接口即可创建新的界面插件，
支持任何 JavaScript 运行环境。
            `);
        }
        
        // 将函数暴露到全局作用域
        window.startWebChat = startWebChat;
        window.showPluginInfo = showPluginInfo;
    </script>
</body>
</html>
