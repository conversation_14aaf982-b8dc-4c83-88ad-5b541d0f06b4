hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@inquirer/checkbox@4.2.2(@types/node@24.3.0)':
    '@inquirer/checkbox': private
  '@inquirer/confirm@5.1.16(@types/node@24.3.0)':
    '@inquirer/confirm': private
  '@inquirer/core@10.2.0(@types/node@24.3.0)':
    '@inquirer/core': private
  '@inquirer/editor@4.2.18(@types/node@24.3.0)':
    '@inquirer/editor': private
  '@inquirer/expand@4.0.18(@types/node@24.3.0)':
    '@inquirer/expand': private
  '@inquirer/external-editor@1.0.1(@types/node@24.3.0)':
    '@inquirer/external-editor': private
  '@inquirer/figures@1.0.13':
    '@inquirer/figures': private
  '@inquirer/input@4.2.2(@types/node@24.3.0)':
    '@inquirer/input': private
  '@inquirer/number@3.0.18(@types/node@24.3.0)':
    '@inquirer/number': private
  '@inquirer/password@4.0.18(@types/node@24.3.0)':
    '@inquirer/password': private
  '@inquirer/prompts@7.8.4(@types/node@24.3.0)':
    '@inquirer/prompts': private
  '@inquirer/rawlist@4.1.6(@types/node@24.3.0)':
    '@inquirer/rawlist': private
  '@inquirer/search@3.1.1(@types/node@24.3.0)':
    '@inquirer/search': private
  '@inquirer/select@4.3.2(@types/node@24.3.0)':
    '@inquirer/select': private
  '@inquirer/type@3.0.8(@types/node@24.3.0)':
    '@inquirer/type': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/node@24.3.0':
    '@types/node': private
  '@types/through@0.0.33':
    '@types/through': private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.15.0:
    acorn: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@6.2.0:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  arg@4.1.3:
    arg: private
  chalk@5.6.0:
    chalk: private
  chardet@2.1.0:
    chardet: private
  cli-cursor@5.0.0:
    cli-cursor: private
  cli-spinners@2.9.2:
    cli-spinners: private
  cli-width@4.1.0:
    cli-width: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  create-require@1.1.1:
    create-require: private
  diff@4.0.2:
    diff: private
  emoji-regex@10.4.0:
    emoji-regex: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  iconv-lite@0.6.3:
    iconv-lite: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-interactive@2.0.0:
    is-interactive: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  log-symbols@6.0.0:
    log-symbols: private
  make-error@1.3.6:
    make-error: private
  mimic-function@5.0.1:
    mimic-function: private
  mute-stream@2.0.0:
    mute-stream: private
  onetime@7.0.0:
    onetime: private
  restore-cursor@5.1.0:
    restore-cursor: private
  run-async@4.0.6:
    run-async: private
  rxjs@7.8.2:
    rxjs: private
  safer-buffer@2.1.2:
    safer-buffer: private
  signal-exit@4.1.0:
    signal-exit: private
  stdin-discarder@0.2.2:
    stdin-discarder: private
  string-width@7.2.0:
    string-width: private
  strip-ansi@7.1.0:
    strip-ansi: private
  tslib@2.8.1:
    tslib: private
  type-fest@0.21.3:
    type-fest: private
  undici-types@7.10.0:
    undici-types: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  yn@3.1.1:
    yn: private
  yoctocolors-cjs@2.1.3:
    yoctocolors-cjs: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Tue, 26 Aug 2025 08:13:17 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v10
virtualStoreDir: C:\Users\<USER>\workspace\cli\node_modules\.pnpm
virtualStoreDirMaxLength: 60
